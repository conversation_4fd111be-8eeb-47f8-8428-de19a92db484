import HeroComponent from "@/components/Hero";
import {BsChatSquareQuote, Bs<PERSON>hatsapp} from "react-icons/bs";
import Container from "@mui/material/Container";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Image from "next/image";
import Button from "@mui/material/Button";
import React from "react";
import Link from "next/link";
import ReserveForm from "@/components/forms/ReserveForm";
import pxToRem from "@/ThemeRegistry/pxToRem";
import Events from "@/components/Events";
// import YearBanner from "@/components/YearBanner";

export default async function Home() {
  return (
    <>
      <HeroComponent />

      <Events />
      <div
        className="review-widget_net"
        data-uuid="9d33039b-9d4f-4a17-be3f-1a4b0a4950dc"
        data-template="10"
        data-lang="de"
        data-theme="dark"
      />

      <br />
      <Divider />
      {/* <Divider /> */}
      <ReserveForm />
      {/* <Divider /> */}
      {/* https://images.ctfassets.net/87jhdyn6f199/LnCtjV6fF1YuDLErImOdy/2e733b1476865b1304ad23e0cdf9d1a8/winteraktion.jpg */}
      <Box sx={{
        mt: 8,
        background: "url(https://images.ctfassets.net/87jhdyn6f199/3MG1zMUNfXupC1WeANfhjn/1bf78f115f2f87539da8c17d9f840ee1/sommeraktion.jpg)",
        height: {xs: "270px", sm: "500px", xl: "600px"},
        width: "100%",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "bottom"
      }} >
        <Divider />
        <Container sx={{
          pt: {xs: 1, sm: 10}
        }}>
          <Grid container spacing={1} alignItems="center">
            <Grid item xs={12} sx={{textAlign: "center"}}>
              <Typography variant="h1" component="h2" gutterBottom sx={{fontSize: `clamp(${pxToRem(25)}, 7vw, ${pxToRem(45)})`}}>
              “VIBES - FÜR ALLE DIE MEHR WOLLEN”
              </Typography>
            </Grid>
            <Grid item xs={12} textAlign="center">
              <BsChatSquareQuote fontSize={45} style={{color: "#67f756"}} />
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Divider />
      <Box sx={{
        position: "relative",
        height: "100%",
      }}>
        <Box sx={{
          position: "absolute",
          top: 0,
          left: 0,
          height: "100%",
          width: "100%",
          background: "url(https://images.ctfassets.net/87jhdyn6f199/1AuR55SkhlsH1dbuAVm7It/0e01d47b2cbc9ef3b134f61244d94f9b/iefubvef.png)",
          backgroundPosition: "top left",
          backgroundSize: "285px auto",
          backgroundRepeat: "no-repeat"
        }} />
        <Container sx={{my: 8, position: {xs: "unset", md: "relative"}}}>
          <Grid container spacing={6} alignItems={"center"}>
            <Grid item xs={12} md={6}>
              <Image
                src="https://images.ctfassets.net/87jhdyn6f199/3l4ylLkF1qrWJUz4ldSh8P/91136f6cfe0b8f21d35e13fe2c50af0a/photo_2023-11-05_17-32-09.jpg"
                alt="test"
                height={0}
                width={0}
                sizes="100vw"
                className="image" />
            </Grid>
            <Grid item xs={12} md={6} >
              <Typography variant="body1" gutterBottom display={"block"} color={"primary"} fontSize={22}>Über uns</Typography>
              <Typography variant="h1" gutterBottom display={"block"} fontSize={{xs: 25, md: 35}}>Genieße die Exklusivität von Vibes Rooftop</Typography>
              <Typography variant="body1" gutterBottom display={"block"} color={"text.secondary"}>Willkommen bei Vibes Rooftop – dem exklusiven Shisha Bar und Eventort, der deine Sinne verzaubert und dir unvergessliche Momente in einer luxuriösen Umgebung bietet. Unsere atemberaubende Dachterrasse inmitten der malerischen Schweizer Landschaft lädt dich ein, den Alltag zu vergessen und dich in eine Welt der Raffinesse und Entspannung zu entführen.</Typography>
              <Link href="/kontakt">
                <Button variant="outlined" color="primary" sx={{mt: 2}}>
                Buche jetzt!
                </Button>
              </Link>
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Box sx={{
        position: "relative",
        height: "100%",
      }}>
        <Box sx={{
          position: "absolute",
          top: 0,
          left: 0,
          height: "100%",
          width: "100%",
          background: "url(https://images.ctfassets.net/87jhdyn6f199/24H1Nuws4G6SgfJSS3b11h/892bdf83f3b546d4ed780bf27f7c7aff/o4itgj4t.png)",
          backgroundPosition: "bottom right",
          backgroundSize: "285px auto",
          backgroundRepeat: "no-repeat"
        }} />
        <Container sx={{my: 8, position: {xs: "unset", md: "relative"}}}>
          <Grid container spacing={6} alignItems={"center"} flexDirection={{xs: "column", md: "row-reverse"}}>
            <Grid item xs={12} md={6}>
              <Image
                src="https://images.ctfassets.net/87jhdyn6f199/3Pu29EwF9cGOKrmizyFPmC/9a7d3e30ed70233d00fd36ff686b0504/photo_2023-11-06_12-24-14.jpg"
                alt="test"
                height={0}
                width={0}
                sizes="100vw"
                className="image" />
            </Grid>
            <Grid item xs={12} md={6} >
              <Typography variant="body1" gutterBottom display={"block"} color={"primary"} fontSize={22}>Unsere Lounge</Typography>
              <Typography variant="h1" gutterBottom display={"block"} fontSize={{xs: 25, md: 35}}>Verbringen Sie die perfekte Zeit mit Freunden</Typography>
              <Typography variant="body1" gutterBottom display={"block"} color={"text.secondary"}>In unserer Lounge erlebst du die vollendete Symbiose aus Stil, Komfort und Geselligkeit. Hier kannst du die perfekte Zeit mit Freunden verbringen, während du von der atemberaubenden Aussicht auf die umliegende Schweizer Landschaft fasziniert wirst. Unsere gemütlichen Sitzgelegenheiten und das elegante Ambiente schaffen den idealen Rahmen für anregende Gespräche, gemeinsames Lachen und unvergessliche Erlebnisse.</Typography>
              <Link href={"/gallerie"}>
                <Button variant="outlined" color="primary" sx={{mt: 2}}>
                Galerie ansehen
                </Button>
              </Link>
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Box component="section"
        sx={{
          position: "relative",
          background: "url(https://images.ctfassets.net/87jhdyn6f199/2klbM9zIZ9qFPUUuCpmfyl/e3a303a5266a74cb58d87ca5795789ee/hookah.png)",
          maxHeight: "100%",
          backgroundAttachment: "fixed",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}>
        <Box sx={{
          position: "absolute",
          top: 0,
          left: 0,
          height: "100%",
          width: "100%",
          backgroundColor: "transparent",
          backgroundImage: "linear-gradient(180deg, #09090B 0%, #09090B 100%)",
          opacity: 0.75
        }} />
        <Container sx={{position: "relative", py: 6}}>
          <Container maxWidth="md" sx={{textAlign: "center"}}>
            <Typography variant="h1" component="h2" color="primary" gutterBottom  fontSize={{xs: 25, md: 35}}>
            Feiere deine Events bei uns!
            </Typography>
            <Typography variant="body1" component="h3" fontSize={16} sx={{color: "text.secondary"}} mb={5}>
            Unvergessliche Geburtstage und Partys
            </Typography>
          </Container>
          <Grid container spacing={7} sx={{flexDirection: {xs: "column-reverse", md: "row"}}}>
            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Image
                    src="https://images.ctfassets.net/87jhdyn6f199/7ooWbJ06KiPYPrVxrKfNNi/35e992f2c15769cc80c822a764ecb537/IMG_0007.JPG"
                    alt="vibes-rooftop"
                    height={0}
                    width={0}
                    sizes="90vw"
                    className="image" />
                </Grid>
                <Grid item xs={6}>
                  <Image
                    src="https://images.ctfassets.net/87jhdyn6f199/4yYVyJ0L5tR2igWpjU5MZj/c28503ae9bb9143b8d4f4f7581c45758/photo_2023-11-06_12-27-04.jpg"
                    alt="vibes-rooftop"
                    height={0}
                    width={0}
                    sizes="100vw"
                    className="image" />
                </Grid>
                <Grid item xs={6}>
                  <Image
                    src="https://images.ctfassets.net/87jhdyn6f199/4vehjxrR07huEAfiAAgV05/416c531c3f923598a7ecb4656baf6ff2/drink.jpg"
                    alt="test"
                    height={0}
                    width={0}
                    sizes="100vw"
                    className="image" />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h3" color="primary" mt={2} gutterBottom fontSize={{xs: 20, md: 25}}>
              Unvergessliche Events bei Vibes Rooftop
              </Typography>
              <Typography variant="body1" color={"text.secondary"}>
              Mach deinen besonderen Tag unvergesslich bei Vibes Rooftop! Unsere luxuriöse Atmosphäre, gepaart mit fairen Preisen und exzellentem Service, bietet den perfekten Rahmen für deine Geburtstagsfeier oder private Party. Erlebe unbeschwerte Stunden in einer einzigartigen Umgebung auf unserer Dachterrasse, während wir uns um alles kümmern, damit du und deine Gäste entspannt feiern könnt.
              </Typography>
              <Typography variant="h3" color="primary" mt={2} gutterBottom fontSize={{xs: 20, md: 25}}>
              Exklusive Events mit Stil
              </Typography>
              <Typography variant="body1" color={"text.secondary"}>
              Ob Firmenfeiern, Jubiläen oder private Anlässe – bei Vibes Rooftop wird dein Event zu einem außergewöhnlichen Erlebnis. Genieße die perfekte Kombination aus erstklassigem Service, angenehmer Atmosphäre und atemberaubendem Ausblick. Mit uns werden deine besonderen Momente stilvoll und einzigartig.
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Divider />
      <Grid container spacing={0} my={5} alignItems={"center"}>
        <Grid item xs={12} md={8}>
          <Grid container>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/1SSfmxI2FCwLG0Ce3np27J/bb423073b0f0099c918540962f799684/hookah.jpg"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/56DVoyLC2c2CexEqKJH2Ky/bb902e800d824f0ebc96e2d065e609d4/photo_2023-11-06_12-25-23.jpg"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/7ooWbJ06KiPYPrVxrKfNNi/35e992f2c15769cc80c822a764ecb537/IMG_0007.JPG"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/5rsv75gR8HM4oDvAkVjCph/b6911ea7e25f716e792ddabf50fd7162/cooktail.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/4vehjxrR07huEAfiAAgV05/416c531c3f923598a7ecb4656baf6ff2/drink.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/1WYNI0v3rkwMZvJPhfG4cm/42c7620097d4c702939417f594f84d19/vibes.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
          </Grid>
        </Grid>
        <Grid
          item
          xs={12}
          md={4}
          sx={{ "&:hover": { color: "#67f756" }, ml: {xs: 2, md: 0}, mt: {xs: 3, md: 0} }}
        >
          <a href="https://wa.me/41763652300">
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <BsWhatsapp fontSize={30} color="#67f756" />
              <Typography variant="body1" fontWeight={300}>Kontaktieren Sie uns über WhatsApp</Typography>
            </Box>
          </a>
        </Grid>
      </Grid>
      {/* <section>
        <YearBanner />
      </section> */}
    </>
  );
}
