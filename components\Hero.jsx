import React from "react";
import { Typo<PERSON>, <PERSON><PERSON>, Container, Box } from "@mui/material";
import pxToRem from "@/ThemeRegistry/pxToRem";

const HeroSection = {
  position: "relative",
  top: { xs: -30, sm: -60 },
  height: { xs: "75vh", md: "85vh" },
  width: "100%",
  overflow: "hidden",
};

const VideoBackground = {
  width: "100%",
  height: "100%",
  objectFit: "cover",
};

const Overlay = {
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  backgroundColor: "rgba(0, 0, 0, 0.3)",
};

const HeroSe = () => {
  return (
    <Box sx={HeroSection}>
      <video autoPlay muted loop playsInline style={VideoBackground}>
        <source
          src="https://videos.ctfassets.net/87jhdyn6f199/610Hv3xbGof0SMwq7OExZs/00bef607dc15ec1b3cf6dec3a7ccdd7b/vid.mp4"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>
      <div style={Overlay} />
      <Container
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          textAlign: { xs: "center", sm: "left" },
          color: "#ffffff",
          zIndex: 1,
        }}
      >
        <Box sx={{ maxWidth: "800px" }}>
          <Typography
            variant="body1"
            component="h3"
            color="primary"
            mb={1}
            fontSize={25}
          >
            Vibes Rooftop
          </Typography>
          <Typography
            variant="h1"
            sx={{ fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(65)})` }}
          >
            ÜBER DEN DÄCHERN VON BASEL
          </Typography>
          <Typography
            variant="h4"
            color="#C6C6C6"
            component="h4"
            my={1}
            sx={{ fontSize: { xs: 16, md: 19 } }}
          >
            Erleben Sie exklusive Shisha und Cocktails in unserem Lounge-Bar in
            Basel
          </Typography>
          <a href="#form">
            <Button variant="outlined" sx={{ mt: 1 }}>
              Jetzt reservieren!
            </Button>
          </a>
        </Box>
      </Container>
    </Box>
  );
};

const HeroComponent = () => {
  return (
    <>
      <HeroSe />
    </>
  );
};

export default HeroComponent;
