"use client";
import React from "react";
import "photoswipe/dist/photoswipe.css";
import propTypes from "prop-types";
import { Gallery, Item } from "react-photoswipe-gallery";
import Image from "next/image";
import { Grid, Container, Typography, Divider, Box } from "@mui/material";

const ImageGallery = ({images}) => {
  return (
    <>
      <Box sx={{pt: 2}}>
        <Typography variant="h1" textAlign={"center"}>
        Gallerie
        </Typography>
        <Typography variant="h6" textAlign={"center"} color={"primary.main"}>
        Vibes Rooftop
        </Typography>
      </Box>
      <Divider sx={{my: 2}}/>
      <Gallery id="vibes-rooftop-gallery">
        <Container maxWidth="xl">
          <Grid container spacing={4}>
            {images && images.length && images.map((media) => {
              const { fields, sys } = media;
              const isVideo = fields.file.contentType.startsWith("video");

              return (
                <Grid key={sys.id} item xs={12} sm={6} md={4} lg={3}>
                  {isVideo ? (
                    <Item
                      id={sys.id}
                      original={fields.file.url}
                      thumbnail={fields.file.url}
                      width="800"
                      height="800"
                    >
                      {({ ref, open }) => (
                        <div className="image-container">
                          <video ref={ref} onClick={open} style={{position: "absolute"}} controls className="image">
                            <source src={fields.file.url} type={fields.file.contentType} />
                            Your browser does not support the video tag.
                          </video>
                        </div>
                      )}
                    </Item>
                   
                  ) : (
                    <Item
                      id={sys.id}
                      original={fields.file.url}
                      thumbnail={fields.file.url}
                      width="800"
                      height="800"
                    >
                      {({ ref, open }) => (
                        <div className="image-container">
                          <Image ref={ref} onClick={open} src={fields.file.url} fill alt="pic" className="image" />
                        </div>
                      )}
                    </Item>
                  )}
                </Grid>
              );
            })}

          </Grid>
        </Container>
      </Gallery>
    </>
  );
};

export default ImageGallery;

ImageGallery.propTypes = {
  images: propTypes.array.isRequired
};