import React, { lazy, Suspense } from "react";
const LazyMap = lazy(() => import("../map/Map"));
import {
  Box,
  Container,
  Typography,
  Grid,
  Divider,
  Chip,
  Avatar,
} from "@mui/material";
import { BsInstagram } from "react-icons/bs";
import { BsWhatsapp } from "react-icons/bs";
import { BsTelephoneInbound } from "react-icons/bs";
import { MdOutlineAttachEmail } from "react-icons/md";
import pxToRem from "@/ThemeRegistry/pxToRem";
import Link from "next/link";

function Footer() {
  return (
    <>
      <Divider />
      <Box sx={{ position: "relative", height: "100%" }}>
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "100%",
            width: "100%",
            background:
              "url(https://images.ctfassets.net/87jhdyn6f199/2N4spdhhraV9UwP8h2oVus/c8af39f3de5057fcb64ba7057a81be29/premium-hookah-charcoal_533x_copy.png)",
            backgroundPosition: "bottom right",
            backgroundSize: "180px auto",
            backgroundRepeat: "no-repeat",
            zIndex: -1,
          }}
        />
        <Container sx={{ position: "relative", my: 5, zIndex: 1 }}>
          <Typography>In Kontakt kommen</Typography>
          <Typography
            variant="h1"
            component={"h2"}
            my={2}
            sx={{ fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(40)})` }}
          >
            Täglich geöffnet, von Mittag bis spät in die Nacht
          </Typography>
          <Typography variant="body1">
            Entdecke unsere internationale Shisha-Bar mit frischen Aromen.
            Tauche ein in eine Welt von Geschmack und Genuss, von erlesenen
            Gewürzen bis hin zu exquisiten Tabaksorten.
          </Typography>
          <Grid
            container
            spacing={5}
            my={3}
            flexDirection={{ xs: "column-reverse", md: "row" }}
          >
            <Grid item xs={12} md={6}>
              <Typography
                variant="h1"
                component={"h2"}
                sx={{ fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(40)})` }}
              >
                Kontakt
              </Typography>
              <Grid container spacing={2} my={2}>
                <Grid item xs={6} sx={{ "&:hover": { color: "#67f756" } }}>
                  <a href="https://www.instagram.com/vibes.rooftop_lounge/">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <BsInstagram fontSize={30} color="#67f756" />
                      <p>Instagram</p>
                    </Box>
                  </a>
                </Grid>
                <Grid
                  item
                  xs={6}
                  sx={{ "&:hover": { color: "#67f756" } }}
                  my={0.2}
                >
                  <a href="https://wa.me/41763652300">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <BsWhatsapp fontSize={30} color="#67f756" />
                      <p>WhatsApp</p>
                    </Box>
                  </a>
                </Grid>
                <Grid
                  item
                  xs={6}
                  sx={{ "&:hover": { color: "#67f756" } }}
                  my={0.2}
                >
                  <a href="tel:+41763652300">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <BsTelephoneInbound fontSize={30} color="#67f756" />
                      <p>Telefon</p>
                    </Box>
                  </a>
                </Grid>
                <Grid
                  item
                  xs={6}
                  sx={{ "&:hover": { color: "#67f756" } }}
                  my={0.2}
                >
                  <a href="mailto:<EMAIL>">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <MdOutlineAttachEmail fontSize={30} color="#67f756" />
                      <p>Email</p>
                    </Box>
                  </a>
                </Grid>
              </Grid>
              <a
                href="https://search.google.com/local/reviews?placeid=ChIJ6XA3uru5kUcRdxIJa4pkxP0"
                target="_blank"
              >
                <Chip
                  sx={{
                    my: 2,
                  }}
                  avatar={
                    <Avatar
                      alt="Natacha"
                      src="https://images.ctfassets.net/87jhdyn6f199/1yXN9Wwz1yo5rRWcqI0fX9/19494c6b97602e2322d1c654d69317c8/colourful-google-logo-in-dark.webp"
                    />
                  }
                  label="Klicken Sie hier, um uns auf Google zu bewerten!"
                  variant="outlined"
                />
              </a>
              <Typography
                variant="h1"
                component={"h2"}
                pt={1}
                sx={{ fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(40)})` }}
              >
                ÖFFNUNGSZEITEN
              </Typography>
              <Grid
                container
                justifyContent={"space-between"}
                alignItems={"center"}
                gap={3}
                my={2}
              >
                <Typography>Mo-Do</Typography>
                <Grid flexGrow={1}>
                  <Divider
                    variant="fullWidth"
                    sx={{
                      borderBottomWidth: "1px",
                      borderStyle: "solid",
                      borderColor: "#67f756",
                      opacity: 0.7,
                    }}
                  />
                </Grid>
                <Typography>17:00 - 24:00</Typography>
              </Grid>
              <Grid
                container
                justifyContent={"space-between"}
                alignItems={"center"}
                gap={3}
                my={2}
              >
                <Typography>Fr+Sa</Typography>
                <Grid flexGrow={1}>
                  <Divider
                    variant="fullWidth"
                    sx={{
                      borderBottomWidth: "1px",
                      borderStyle: "solid",
                      borderColor: "#67f756",
                      opacity: 0.7,
                    }}
                  />
                </Grid>
                <Typography>17:00 - 02:00</Typography>
              </Grid>
              <Grid
                container
                justifyContent={"space-between"}
                alignItems={"center"}
                gap={3}
                my={2}
              >
                <Typography>So</Typography>
                <Grid flexGrow={1}>
                  <Divider
                    variant="fullWidth"
                    sx={{
                      borderBottomWidth: "1px",
                      borderStyle: "solid",
                      borderColor: "#67f756",
                      opacity: 0.7,
                    }}
                  />
                </Grid>
                <Typography>15:00 - 24:00</Typography>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} textAlign={"center"}>
              <Suspense fallback={<div>Loading...</div>}>
                <LazyMap
                  value={[7.59743, 47.56646]}
                  direction={`
                  <h2 style="color: #000">Vibes Rooftop</h2>
                  <a href="https://www.google.com/maps/dir/?api=1&destination=Haltingerstrasse%20104,%204057%20Basel,%20Schweiz" target="_blank" style="color: green; text-decoration: underline; font-size: 14px;">Directions</a>
                `}
                />
              </Suspense>
            </Grid>
          </Grid>
          <Divider />
          <Typography
            textAlign={"center"}
            mt={3}
            variant="h2"
            sx={{ fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(40)})` }}
          >
            Parking Location
          </Typography>
          <Grid item xs={12} mt={3} textAlign={"center"}>
            <Suspense fallback={<div>Loading...</div>}>
              <LazyMap
                value={[7.594505261686455, 47.56566131372293]}
                direction={`
              <h2 style="color: #000">Parkhaus Claramatte</h2>
              <a href="https://www.google.com/maps/dir/?api=1&destination=Klingentalstrasse+25,+4057+Basel,+Switzerland" target="_blank" style="color: green; text-decoration: underline; font-size: 14px;">Directions</a>
              `}
              />
            </Suspense>
          </Grid>
        </Container>
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "100%",
            width: "100%",
            background:
              "url(https://images.ctfassets.net/87jhdyn6f199/Hbcz2FYAq7agT1NGxxDHd/398d1673d51b2d7d992173c0c0517fb4/1.png)",
            backgroundPosition: "top left",
            backgroundSize: "180px auto",
            backgroundRepeat: "no-repeat",
          }}
        />
      </Box>
      <Divider />
      <Container sx={{ my: 4 }}>
        <Grid
          container
          justifyContent={"space-between"}
          flexDirection={{ xs: "column-reverse", md: "row" }}
          textAlign={{ xs: "center", md: "unset" }}
          gap={1}
        >
          <Typography variant="body2">
            Copyright &copy; {new Date().getFullYear()} Vibes Rooftop All right
            reserved.
          </Typography>
          <Typography variant="body2">
            <a href="https://www.instagram.com/vibes.rooftop_lounge/">
              Instagram: <span style={{ color: "#67f756" }}>Vibes_Rooftop</span>
            </a>
          </Typography>
          <Typography variant="body2">
            <Link href="/privacy-policy">Privacy Policy</Link>
          </Typography>
        </Grid>
      </Container>
    </>
  );
}

export default Footer;
