* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: transparent;
  background-image: linear-gradient(180deg, #09090b 0%, #09090b 100%);
}

a {
  color: inherit;
  text-decoration: none;
}

.image-container {
  position: relative;
  width: 100%;
  padding-top: 100%;
}
.image {
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/* Swiss National Day floating animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
