import { createTheme } from "@mui/material/styles";
import "@fontsource-variable/montserrat";
import pxToRem from "./pxToRem";

// const worksans = Work_Sans({
//   weight: ["300", "400", "500", "600", "700", "800", "900"],
//   style: ["normal", "italic"],
//   subsets: ["latin",],
// });


const theme = createTheme({
  palette: {
    mode: "dark",
    primary: {
      main: "#67f756",
      light: "#a4fa9a",
      dark: "#67f710",
      contrastText: "#fff",
    },
    secondary: {
      main: "#4a1d1f",
      light: "#f73da3",
      dark: "#1e0c0c",
      contrastText: "#fafafa",
    },
    text: {
      primary: "#fff",
      secondary: "#C6C6C6",
    },
    success: {
      main: "#00ba3e",
      light: "#33c162",
      dark: "#017b2c",
      contrastText: "#fafafa",
    },
    error: {
      main: "#c72e2e",
      light: "#c35555",
      dark: "#8a1e1e",
    },
  },
  typography: {
    fontFamily: "Montserrat Variable, sans-serif",
    fontSize: 16,
    htmlFontSize: 16,
    h1: {
      fontSize: pxToRem(44),
      fontWeight: 700,
      lineHeight: "1.2em",
    },
    h2: {
      fontSize: pxToRem(38),
      fontWeight: 600,
      lineHeight: 1.2,
      letterSpacing: "0em",
    },
    h3: {
      fontSize: pxToRem(34),
      fontWeight: 500,
    },
    h4: {
      fontSize: pxToRem(30),
      fontWeight: 400,
    },
    h5: {
      fontSize: pxToRem(26),
      fontWeight: 400,
    },
    h6: {
      fontSize: pxToRem(22),
      fontWeight: 400,
    },
    caption: {
      fontSize: pxToRem(12),
      fontWeight: 300,
      letterSpacing: 1,
    },
    button: {
      fontSize: pxToRem(16),
      fontWeight: 400,
      lineHeight: "normal",
    }
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        backgroundColor: "transparent",
        backgroundImage: "linear-gradient(180deg, #09090B 0%, #09090B 100%)",
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          padding: "12px 25px",
          borderRadius: pxToRem(0),
          fontWeight: 500,
          fontSize: pxToRem(19),
          textTransform: "capitalize",
        },
        outlinedPrimary: {
          color: "#67f756",
          transition: "0.3s ease-in-out",
          border: "1px solid #67f756",
          borderRadius: "3px",
          "&:hover": {
            backgroundColor: "#67f756",
            color: "#000"
          }
        },
      }
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: "#fdf9ed",
          color: "#4a1d1f",
          boxShadow: 1,
          fontSize: 11,
        },
        arrow: {
          color: "#fdf9ed",
        }
      }
    },
  },
});

export default theme;
