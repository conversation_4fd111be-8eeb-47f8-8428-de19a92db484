"use client";

import React, { useState } from "react";
import Navigation from "./Navigation";
import Image from "next/image";
import AppBar from "@mui/material/AppBar";
import Link from "next/link";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import Drawer from "@mui/material/Drawer";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import { Box, Container, Typography } from "@mui/material";
import { RiMenu4Line } from "react-icons/ri";
import { AiOutlineClose } from "react-icons/ai";
import { BiHomeAlt } from "react-icons/bi";
// import { BsMenuUp } from "react-icons/bs";
import { LuGalleryVerticalEnd } from "react-icons/lu";
import { BsInfoSquare } from "react-icons/bs";
import { MdOutlineContacts } from "react-icons/md";

const drawerWidth = "100%";

function Header() {
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <div style={{margin: "70px 0px", backgroundColor: "transparent"}}>
      {/* Large Screen AppBar */}
      <AppBar position="fixed" component="div" sx={{ display: { xs: "none", md: "block", }, bgcolor: "transparent", color: "text.secondary", py: 0.5, WebkitBoxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)",
        MozBoxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)",
        boxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)", backdropFilter: "blur(8px)" }}>
        <Container sx={{maxWidth: {xs: "xs", md: "xl"}}}>
          <Toolbar sx={{justifyContent: "space-between"}}>
            <Link href={"/"}>
              <Image src="/vibes.webp" width={140} height={40} alt="Logo" />
            </Link>
            <Navigation />
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Screen Drawer */}
      <AppBar position="fixed" component="div" sx={{ display: { xs: "block", md: "none" },
        bgcolor: "transparent", color: "text.secondary", py: 0.5, WebkitBoxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)",
        MozBoxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)",
        boxShadow: "0px -1px 5px 0px rgba(186,177,186,0.4)", backdropFilter: "blur(8px)"}}>
        <Toolbar>
          <Box sx={{ mt: 1, p: 0.5, flexGrow: 1 }}>
            <Link href={"/"}>
              <Image src="/vibes.webp" width={100} height={30} alt="Logo" />
            </Link>
          </Box>
          <IconButton size="medium" color="inherit" onClick={toggleMobileMenu} sx={{zIndex: 3}}>
            {!isMobileMenuOpen ? (
              <RiMenu4Line />
            ) : (
              <AiOutlineClose />
            )}
          </IconButton>
        </Toolbar>
      </AppBar>
      <Drawer
        sx={{ display: { xs: "block", md: "none" },
          width: drawerWidth,
          zIndex: 2,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            bgcolor: "transparent",
            backdropFilter: "blur(15px)",
            color: "#ffffff",
            boxSizing: "border-box",
            mt: 8.3,
          }, }}
        anchor="top"
        open={isMobileMenuOpen}
        onClose={toggleMobileMenu}
      >
        <List>
          <Link href={"/"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <BiHomeAlt />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Home
                </Typography>
              </Box>
            </ListItem>
          </Link>
          {/* <Link href={"/menuekarte"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <BsMenuUp />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Menükarte
                </Typography>
              </Box>
            </ListItem>
          </Link> */}
          <Link href={"/gallerie"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <LuGalleryVerticalEnd />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Gallerie
                </Typography>
              </Box>
            </ListItem>
          </Link>
          <Link href={"/uber-uns"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <BsInfoSquare />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Über uns
                </Typography>
              </Box>
            </ListItem>
          </Link>
          <Link href={"/kontakt"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <MdOutlineContacts />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Kontakt
                </Typography>
              </Box>
            </ListItem>
          </Link>
        </List>
      </Drawer>
    </div>
  );
}

export default Header;
